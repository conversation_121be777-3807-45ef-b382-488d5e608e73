#!/usr/bin/env python3
"""
集成图像问答功能测试脚本
测试RAG系统中集成的图像内容分析功能
"""

import os
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rag.rag_system import RAGSystem
from rag.pdf_processor import PDFProcessor

def test_integrated_image_qa():
    """测试集成的图像问答功能"""
    try:
        logger.info("🚀 开始测试集成图像问答功能")
        
        # 1. 初始化系统
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        pdf_processor = PDFProcessor()
        if not pdf_processor.initialize():
            logger.error("PDF处理器初始化失败")
            return False
        
        # 2. 检查图像分析功能状态
        image_stats = rag_system.get_image_analysis_stats()
        logger.info(f"图像分析功能状态: {image_stats}")
        
        if not image_stats.get("image_analysis_enabled"):
            logger.warning("图像分析功能未启用，将只测试常规问答")
        
        # 3. 测试问题列表
        test_questions = [
            "这个图表显示了什么数据？",
            "收入增长趋势如何？",
            "图像中有哪些具体的数值？",
            "这是什么类型的图表？",
            "财务数据的时间范围是什么？",
            "利润率的变化情况如何？",
            "图表中最高的数值是多少？",
            "这个饼图显示了什么分布？"
        ]
        
        # 4. 执行测试
        results = []
        for i, question in enumerate(test_questions, 1):
            logger.info(f"\n📝 测试问题 {i}: {question}")
            
            try:
                # 使用增强问答方法
                result = rag_system.answer_question_with_enhanced_search(question, top_k=5)
                
                logger.info(f"✅ 回答: {result['answer'][:200]}...")
                logger.info(f"置信度: {result['confidence_score']:.2f}")
                logger.info(f"处理时间: {result['processing_time']:.2f}秒")
                logger.info(f"包含图像分析: {result['has_image_analysis']}")
                
                results.append({
                    "question": question,
                    "answer": result['answer'],
                    "confidence": result['confidence_score'],
                    "has_image_analysis": result['has_image_analysis'],
                    "processing_time": result['processing_time']
                })
                
            except Exception as e:
                logger.error(f"❌ 问题 {i} 测试失败: {e}")
                results.append({
                    "question": question,
                    "error": str(e)
                })
        
        # 5. 生成测试报告
        generate_test_report(results, image_stats)
        
        logger.info("✅ 集成图像问答功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def generate_test_report(results, image_stats):
    """生成测试报告"""
    try:
        report_lines = [
            "# 集成图像问答功能测试报告\n",
            f"## 系统状态",
            f"- 图像分析启用: {image_stats.get('image_analysis_enabled', False)}",
            f"- 图像分析器可用: {image_stats.get('image_analyzer_available', False)}",
            f"- 图像缓存大小: {image_stats.get('image_cache_size', 0)}",
            "",
            "## 测试结果\n"
        ]
        
        successful_tests = 0
        total_tests = len(results)
        image_analysis_count = 0
        total_processing_time = 0
        
        for i, result in enumerate(results, 1):
            if "error" not in result:
                successful_tests += 1
                if result.get("has_image_analysis"):
                    image_analysis_count += 1
                total_processing_time += result.get("processing_time", 0)
                
                report_lines.extend([
                    f"### 测试 {i}: {result['question']}",
                    f"**回答**: {result['answer'][:300]}{'...' if len(result['answer']) > 300 else ''}",
                    f"**置信度**: {result['confidence']:.2f}",
                    f"**包含图像分析**: {'是' if result['has_image_analysis'] else '否'}",
                    f"**处理时间**: {result['processing_time']:.2f}秒",
                    ""
                ])
            else:
                report_lines.extend([
                    f"### 测试 {i}: {result['question']}",
                    f"**错误**: {result['error']}",
                    ""
                ])
        
        # 添加统计信息
        report_lines.extend([
            "## 统计信息",
            f"- 总测试数: {total_tests}",
            f"- 成功测试数: {successful_tests}",
            f"- 成功率: {successful_tests/total_tests*100:.1f}%",
            f"- 包含图像分析的测试: {image_analysis_count}",
            f"- 图像分析使用率: {image_analysis_count/successful_tests*100:.1f}%" if successful_tests > 0 else "- 图像分析使用率: 0%",
            f"- 平均处理时间: {total_processing_time/successful_tests:.2f}秒" if successful_tests > 0 else "- 平均处理时间: N/A",
            ""
        ])
        
        # 保存报告
        report_content = "\n".join(report_lines)
        report_path = Path("integrated_image_qa_test_report.md")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"📊 测试报告已保存到: {report_path}")
        
        # 打印简要统计
        logger.info("📈 测试统计:")
        logger.info(f"   成功率: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
        logger.info(f"   图像分析使用: {image_analysis_count} 次")
        logger.info(f"   平均处理时间: {total_processing_time/successful_tests:.2f}秒" if successful_tests > 0 else "   平均处理时间: N/A")
        
    except Exception as e:
        logger.error(f"生成测试报告失败: {e}")

def test_specific_image_question():
    """测试特定图像问题"""
    try:
        logger.info("🎯 测试特定图像问题")
        
        rag_system = RAGSystem()
        if not rag_system.initialize():
            return False
        
        # 测试特定的图像相关问题
        specific_questions = [
            "请分析这个柱状图中的数据趋势",
            "图表中2023年的收入是多少？",
            "这个饼图显示的各部分占比是什么？",
            "折线图显示的增长率变化如何？"
        ]
        
        for question in specific_questions:
            logger.info(f"\n🔍 特定问题: {question}")
            
            # 执行常规搜索
            regular_results = rag_system.search_knowledge(question, top_k=3, include_image_analysis=False)
            logger.info(f"常规搜索结果数: {len(regular_results)}")
            
            # 执行增强搜索
            enhanced_results = rag_system.search_knowledge(question, top_k=3, include_image_analysis=True)
            logger.info(f"增强搜索结果数: {len(enhanced_results)}")
            
            # 比较结果
            enhanced_count = sum(1 for r in enhanced_results if r.get("image_analysis"))
            logger.info(f"包含图像分析的结果: {enhanced_count}")
            
            if enhanced_count > 0:
                logger.info("✅ 图像分析功能正常工作")
            else:
                logger.warning("⚠️ 未检测到图像分析结果")
        
        return True
        
    except Exception as e:
        logger.error(f"特定图像问题测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🧪 开始集成图像问答功能测试")
    
    try:
        # 基础功能测试
        success1 = test_integrated_image_qa()
        
        # 特定图像问题测试
        success2 = test_specific_image_question()
        
        if success1 and success2:
            logger.info("🎉 所有测试完成！")
        else:
            logger.warning("⚠️ 部分测试失败")
            
    except Exception as e:
        logger.error(f"测试执行失败: {e}")

if __name__ == "__main__":
    main()
