"""
API调用优化模块
提供连接池、缓存、重试机制、并发控制等功能，显著提升API调用性能
"""

import asyncio
import aiohttp
import time
import hashlib
import json
import threading
from typing import Dict, Any, Optional, Callable, List
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class APIOptimizer:
    """API调用优化器"""
    
    def __init__(self, max_workers: int = 10, cache_size: int = 1000, cache_ttl: int = 3600):
        """
        初始化API优化器
        
        Args:
            max_workers: 最大并发工作线程数
            cache_size: 缓存大小
            cache_ttl: 缓存生存时间（秒）
        """
        self.max_workers = max_workers
        self.cache_size = cache_size
        self.cache_ttl = cache_ttl
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 缓存系统
        self._cache = {}
        self._cache_timestamps = {}
        self._cache_lock = threading.RLock()
        
        # 连接池配置
        self.session = self._create_optimized_session()
        
        # 异步会话
        self._async_session = None
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_time': 0,
            'avg_response_time': 0,
            'errors': 0,
            'retries': 0
        }
        self._stats_lock = threading.Lock()
        
        logger.info(f"API优化器初始化完成 - 最大并发: {max_workers}, 缓存大小: {cache_size}")
    
    def _create_optimized_session(self) -> requests.Session:
        """创建优化的HTTP会话"""
        session = requests.Session()
        
        # 重试策略
        retry_strategy = Retry(
            total=3,  # 总重试次数
            backoff_factor=0.5,  # 退避因子
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        # HTTP适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=20,  # 连接池大小
            pool_maxsize=20,     # 每个连接池的最大连接数
            pool_block=False     # 非阻塞模式
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认超时
        session.timeout = (10, 60)  # 连接超时10秒，读取超时60秒
        
        return session
    
    def _generate_cache_key(self, url: str, method: str = "GET", **kwargs) -> str:
        """生成缓存键"""
        try:
            # 创建唯一标识
            cache_data = {
                'url': url,
                'method': method.upper(),
                'params': kwargs.get('params', {}),
                'json': kwargs.get('json', {}),
                'data': kwargs.get('data', {}),
                'headers': {k: v for k, v in kwargs.get('headers', {}).items() 
                           if k.lower() not in ['authorization', 'cookie']}  # 排除敏感信息
            }
            
            cache_str = json.dumps(cache_data, sort_keys=True)
            return hashlib.md5(cache_str.encode()).hexdigest()
        except Exception as e:
            logger.warning(f"生成缓存键失败: {e}")
            return None
    
    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if not cache_key:
            return None
            
        with self._cache_lock:
            if cache_key in self._cache:
                # 检查是否过期
                if time.time() - self._cache_timestamps[cache_key] < self.cache_ttl:
                    with self._stats_lock:
                        self.stats['cache_hits'] += 1
                    logger.debug(f"缓存命中: {cache_key[:8]}...")
                    return self._cache[cache_key]
                else:
                    # 清理过期缓存
                    del self._cache[cache_key]
                    del self._cache_timestamps[cache_key]
            
            with self._stats_lock:
                self.stats['cache_misses'] += 1
            return None
    
    def _set_cache(self, cache_key: str, data: Any) -> None:
        """设置缓存"""
        if not cache_key:
            return
            
        with self._cache_lock:
            # 如果缓存已满，删除最旧的条目
            if len(self._cache) >= self.cache_size:
                oldest_key = min(self._cache_timestamps.keys(), 
                               key=lambda k: self._cache_timestamps[k])
                del self._cache[oldest_key]
                del self._cache_timestamps[oldest_key]
            
            self._cache[cache_key] = data
            self._cache_timestamps[cache_key] = time.time()
            logger.debug(f"缓存设置: {cache_key[:8]}...")
    
    def optimized_request(self, url: str, method: str = "GET", 
                         use_cache: bool = True, timeout: Optional[int] = None,
                         **kwargs) -> requests.Response:
        """
        优化的HTTP请求
        
        Args:
            url: 请求URL
            method: HTTP方法
            use_cache: 是否使用缓存
            timeout: 超时时间
            **kwargs: 其他请求参数
            
        Returns:
            响应对象
        """
        start_time = time.time()
        
        try:
            with self._stats_lock:
                self.stats['total_requests'] += 1
            
            # 检查缓存
            cache_key = None
            if use_cache and method.upper() in ['GET', 'HEAD']:
                cache_key = self._generate_cache_key(url, method, **kwargs)
                cached_response = self._get_from_cache(cache_key)
                if cached_response:
                    logger.info(f"✅ 缓存命中，跳过API调用 ({url})")
                    return cached_response
            
            # 设置超时
            if timeout:
                kwargs['timeout'] = timeout
            elif 'timeout' not in kwargs:
                kwargs['timeout'] = (10, 60)
            
            # 发起请求
            logger.debug(f"🚀 发起API请求: {method} {url}")
            response = self.session.request(method, url, **kwargs)
            
            # 检查响应状态
            response.raise_for_status()
            
            # 缓存成功响应
            if use_cache and cache_key and response.status_code == 200:
                self._set_cache(cache_key, response)
            
            # 更新统计
            elapsed_time = time.time() - start_time
            with self._stats_lock:
                self.stats['total_time'] += elapsed_time
                self.stats['avg_response_time'] = self.stats['total_time'] / self.stats['total_requests']
            
            logger.info(f"✅ API请求成功 (耗时: {elapsed_time:.2f}秒)")
            return response
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            with self._stats_lock:
                self.stats['errors'] += 1
                self.stats['total_time'] += elapsed_time
                self.stats['avg_response_time'] = self.stats['total_time'] / self.stats['total_requests']
            
            logger.error(f"❌ API请求失败 (耗时: {elapsed_time:.2f}秒): {e}")
            raise
    
    async def async_request(self, url: str, method: str = "GET", 
                           use_cache: bool = True, **kwargs) -> aiohttp.ClientResponse:
        """异步HTTP请求"""
        if not self._async_session:
            connector = aiohttp.TCPConnector(
                limit=100,  # 总连接池大小
                limit_per_host=20,  # 每个主机的连接数
                ttl_dns_cache=300,  # DNS缓存时间
                use_dns_cache=True,
            )
            timeout = aiohttp.ClientTimeout(total=60, connect=10)
            self._async_session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
        
        start_time = time.time()
        
        try:
            with self._stats_lock:
                self.stats['total_requests'] += 1
            
            # 检查缓存
            cache_key = None
            if use_cache and method.upper() in ['GET', 'HEAD']:
                cache_key = self._generate_cache_key(url, method, **kwargs)
                cached_response = self._get_from_cache(cache_key)
                if cached_response:
                    logger.info(f"✅ 异步缓存命中，跳过API调用 ({url})")
                    return cached_response
            
            # 发起异步请求
            logger.debug(f"🚀 发起异步API请求: {method} {url}")
            async with self._async_session.request(method, url, **kwargs) as response:
                response.raise_for_status()
                
                # 缓存成功响应
                if use_cache and cache_key and response.status == 200:
                    self._set_cache(cache_key, response)
                
                # 更新统计
                elapsed_time = time.time() - start_time
                with self._stats_lock:
                    self.stats['total_time'] += elapsed_time
                    self.stats['avg_response_time'] = self.stats['total_time'] / self.stats['total_requests']
                
                logger.info(f"✅ 异步API请求成功 (耗时: {elapsed_time:.2f}秒)")
                return response
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            with self._stats_lock:
                self.stats['errors'] += 1
                self.stats['total_time'] += elapsed_time
                self.stats['avg_response_time'] = self.stats['total_time'] / self.stats['total_requests']
            
            logger.error(f"❌ 异步API请求失败 (耗时: {elapsed_time:.2f}秒): {e}")
            raise
    
    def batch_requests(self, requests_list: List[Dict[str, Any]], 
                      max_concurrent: int = None) -> List[Any]:
        """
        批量并发请求
        
        Args:
            requests_list: 请求列表，每个元素包含url, method等参数
            max_concurrent: 最大并发数
            
        Returns:
            响应列表
        """
        if not max_concurrent:
            max_concurrent = min(len(requests_list), self.max_workers)
        
        logger.info(f"🚀 开始批量请求: {len(requests_list)} 个请求，最大并发: {max_concurrent}")
        start_time = time.time()
        
        results = []
        futures = []
        
        # 提交任务到线程池
        for req_params in requests_list:
            future = self.executor.submit(self.optimized_request, **req_params)
            futures.append(future)
        
        # 收集结果
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logger.error(f"批量请求中的单个请求失败: {e}")
                results.append(None)
        
        elapsed_time = time.time() - start_time
        success_count = sum(1 for r in results if r is not None)
        logger.info(f"✅ 批量请求完成: {success_count}/{len(requests_list)} 成功 (总耗时: {elapsed_time:.2f}秒)")
        
        return results
    
    def clear_cache(self) -> None:
        """清理缓存"""
        with self._cache_lock:
            self._cache.clear()
            self._cache_timestamps.clear()
        logger.info("🧹 API缓存已清理")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._stats_lock:
            stats = self.stats.copy()
            stats['cache_hit_rate'] = (stats['cache_hits'] / 
                                     max(stats['cache_hits'] + stats['cache_misses'], 1))
            stats['error_rate'] = stats['errors'] / max(stats['total_requests'], 1)
            return stats
    
    def close(self) -> None:
        """关闭优化器"""
        if self.session:
            self.session.close()
        if self._async_session:
            asyncio.create_task(self._async_session.close())
        self.executor.shutdown(wait=True)
        logger.info("API优化器已关闭")

# 全局优化器实例
_global_optimizer = None

def get_optimizer() -> APIOptimizer:
    """获取全局API优化器实例"""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = APIOptimizer()
    return _global_optimizer

def optimized_api_call(func: Callable) -> Callable:
    """
    API调用优化装饰器
    
    使用方法:
    @optimized_api_call
    def my_api_function():
        # API调用代码
        pass
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        optimizer = get_optimizer()
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            logger.info(f"✅ 优化API调用完成: {func.__name__} (耗时: {elapsed_time:.2f}秒)")
            return result
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ 优化API调用失败: {func.__name__} (耗时: {elapsed_time:.2f}秒): {e}")
            raise
    
    return wrapper

# 便捷函数
def optimized_get(url: str, **kwargs) -> requests.Response:
    """优化的GET请求"""
    return get_optimizer().optimized_request(url, "GET", **kwargs)

def optimized_post(url: str, **kwargs) -> requests.Response:
    """优化的POST请求"""
    return get_optimizer().optimized_request(url, "POST", **kwargs)

def batch_get(urls: List[str], **kwargs) -> List[requests.Response]:
    """批量GET请求"""
    requests_list = [{"url": url, "method": "GET", **kwargs} for url in urls]
    return get_optimizer().batch_requests(requests_list)
