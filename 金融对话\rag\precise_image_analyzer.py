"""
精确图像内容分析器
专门用于精确快速地分析PDF中的图像内容，以便准确回答用户问题
"""

import os
import sys
import time
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from PIL import Image
import numpy as np

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from idconfig.config import Config

try:
    import cv2
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logger.warning("OCR功能不可用，请安装pytesseract和opencv-python")

try:
    from transformers import BlipProcessor, BlipForConditionalGeneration
    import torch
    BLIP_AVAILABLE = True
except ImportError:
    BLIP_AVAILABLE = False
    logger.warning("BLIP模型不可用")

class PreciseImageAnalyzer:
    """精确图像内容分析器"""
    
    def __init__(self):
        self.config = Config()
        
        # 分析缓存
        self._analysis_cache = {}
        self._cache_max_size = 200
        
        # 图像类型检测关键词
        self.chart_keywords = {
            "柱状图": ["bar", "column", "柱状", "条形", "直方图"],
            "折线图": ["line", "trend", "折线", "趋势", "曲线"],
            "饼图": ["pie", "circle", "饼图", "圆形", "占比"],
            "散点图": ["scatter", "point", "散点", "分布"],
            "表格": ["table", "grid", "表格", "数据表", "列表"],
            "流程图": ["flow", "process", "流程", "步骤", "箭头"],
            "组织图": ["org", "hierarchy", "组织", "结构", "层级"]
        }
        
        # 金融关键词
        self.financial_keywords = {
            "数据": ["收入", "利润", "营收", "成本", "费用", "资产", "负债", "现金流", "投资", "股价", "市值"],
            "时间": ["年度", "季度", "月度", "年", "月", "日", "期间", "同比", "环比"],
            "指标": ["增长率", "利润率", "ROE", "ROA", "毛利率", "净利率", "负债率", "流动比率"],
            "行业": ["银行", "保险", "证券", "基金", "信托", "金融", "投资", "理财"]
        }
        
        logger.info("精确图像分析器初始化完成")
    
    def analyze_image_content(self, image_path: str, context_info: Dict[str, str] = None, 
                            user_question: str = None) -> Dict[str, Any]:
        """
        精确分析图像内容，针对用户问题优化
        
        Args:
            image_path: 图像文件路径
            context_info: 上下文信息
            user_question: 用户问题（可选，用于针对性分析）
            
        Returns:
            详细的图像分析结果
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(image_path, context_info, user_question)
            if cache_key in self._analysis_cache:
                logger.info("✅ 使用缓存的图像分析结果")
                return self._analysis_cache[cache_key]
            
            start_time = time.time()
            logger.info(f"🔍 开始精确分析图像: {os.path.basename(image_path)}")
            
            # 加载图像
            image = Image.open(image_path)
            
            # 执行多层次分析
            analysis_result = {
                "image_path": image_path,
                "image_size": image.size,
                "analysis_time": 0,
                "content_type": "unknown",
                "extracted_text": "",
                "detailed_description": "",
                "key_elements": [],
                "financial_data": {},
                "chart_analysis": {},
                "question_relevance": {},
                "confidence_score": 0.0
            }
            
            # 1. 图像类型识别
            content_type = self._identify_content_type(image, context_info)
            analysis_result["content_type"] = content_type
            
            # 2. OCR文字提取
            if OCR_AVAILABLE:
                extracted_text = self._extract_text_with_ocr(image)
                analysis_result["extracted_text"] = extracted_text
            
            # 3. 详细描述生成
            detailed_description = self._generate_detailed_description(image, context_info, content_type)
            analysis_result["detailed_description"] = detailed_description
            
            # 4. 关键元素提取
            key_elements = self._extract_key_elements(image, extracted_text, content_type)
            analysis_result["key_elements"] = key_elements
            
            # 5. 金融数据提取
            if content_type in ["柱状图", "折线图", "饼图", "表格"]:
                financial_data = self._extract_financial_data(extracted_text, context_info)
                analysis_result["financial_data"] = financial_data
            
            # 6. 图表专项分析
            if content_type in ["柱状图", "折线图", "饼图", "散点图"]:
                chart_analysis = self._analyze_chart_content(image, extracted_text, content_type)
                analysis_result["chart_analysis"] = chart_analysis
            
            # 7. 问题相关性分析
            if user_question:
                question_relevance = self._analyze_question_relevance(
                    user_question, analysis_result, context_info
                )
                analysis_result["question_relevance"] = question_relevance
            
            # 8. 计算置信度
            confidence_score = self._calculate_confidence_score(analysis_result)
            analysis_result["confidence_score"] = confidence_score
            
            # 记录分析时间
            analysis_result["analysis_time"] = time.time() - start_time
            
            # 缓存结果
            self._cache_analysis_result(cache_key, analysis_result)
            
            logger.info(f"✅ 图像分析完成 (耗时: {analysis_result['analysis_time']:.2f}秒, "
                       f"置信度: {confidence_score:.2f})")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 图像分析失败: {e}")
            return {"error": str(e), "confidence_score": 0.0}
    
    def _identify_content_type(self, image: Image.Image, context_info: Dict[str, str] = None) -> str:
        """识别图像内容类型"""
        try:
            # 基于上下文文本的类型推断
            if context_info:
                all_text = " ".join(context_info.values()).lower()
                
                for chart_type, keywords in self.chart_keywords.items():
                    if any(keyword in all_text for keyword in keywords):
                        return chart_type
            
            # 基于图像特征的类型推断
            img_array = np.array(image.convert('RGB'))
            height, width = img_array.shape[:2]
            
            # 简单的形状检测
            if width > height * 1.5:  # 宽图可能是折线图或柱状图
                return "折线图"
            elif abs(width - height) < min(width, height) * 0.2:  # 接近正方形可能是饼图
                return "饼图"
            else:
                return "柱状图"  # 默认
                
        except Exception as e:
            logger.warning(f"图像类型识别失败: {e}")
            return "未知类型"
    
    def _extract_text_with_ocr(self, image: Image.Image) -> str:
        """使用OCR提取图像中的文字"""
        try:
            if not OCR_AVAILABLE:
                return ""
            
            # 图像预处理以提高OCR准确性
            img_array = np.array(image.convert('RGB'))
            
            # 转换为灰度图
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # 应用高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 自适应阈值处理
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
            
            # 使用pytesseract进行OCR
            # 配置OCR参数，针对中英文混合文本优化
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
            extracted_text = pytesseract.image_to_string(thresh, config=custom_config)
            
            # 清理提取的文本
            cleaned_text = self._clean_extracted_text(extracted_text)
            
            logger.debug(f"OCR提取文本: {cleaned_text[:100]}...")
            return cleaned_text
            
        except Exception as e:
            logger.warning(f"OCR文字提取失败: {e}")
            return ""
    
    def _clean_extracted_text(self, text: str) -> str:
        """清理OCR提取的文本"""
        try:
            # 移除多余的空白字符
            cleaned = " ".join(text.split())
            
            # 移除特殊字符，保留中文、英文、数字和常用标点
            import re
            cleaned = re.sub(r'[^\u4e00-\u9fff\w\s\.\,\%\-\+\(\)\[\]\/\:：]', '', cleaned)
            
            return cleaned.strip()
            
        except Exception as e:
            logger.warning(f"文本清理失败: {e}")
            return text
    
    def _generate_detailed_description(self, image: Image.Image, context_info: Dict[str, str] = None, 
                                     content_type: str = "unknown") -> str:
        """生成详细的图像描述"""
        try:
            # 构建针对性的提示词
            if content_type == "柱状图":
                prompt = """请详细分析这个柱状图，重点描述：
1. 图表标题和轴标签
2. 数据系列的数量和名称
3. 主要数据值和趋势
4. 时间范围或分类维度
5. 任何显著的数据特征或异常值"""
            elif content_type == "折线图":
                prompt = """请详细分析这个折线图，重点描述：
1. 图表标题和轴标签
2. 折线的数量和代表的指标
3. 数据的时间趋势和变化模式
4. 峰值、谷值和转折点
5. 整体趋势方向（上升、下降、波动）"""
            elif content_type == "饼图":
                prompt = """请详细分析这个饼图，重点描述：
1. 图表标题和总体数据
2. 各个扇形的标签和占比
3. 最大和最小的组成部分
4. 数据的分布特征
5. 任何特殊标注或说明"""
            elif content_type == "表格":
                prompt = """请详细分析这个表格，重点描述：
1. 表格标题和列标题
2. 行数和列数
3. 主要数据类型和数值范围
4. 关键数据和重要信息
5. 表格的整体结构和组织方式"""
            else:
                prompt = """请详细分析这张图像，重点描述：
1. 图像的主要内容和类型
2. 包含的文字信息
3. 数据或信息的组织方式
4. 关键元素和重要细节
5. 图像要传达的主要信息"""
            
            # 添加上下文信息
            if context_info and any(context_info.values()):
                context_text = " ".join([v for v in context_info.values() if v])[:200]
                prompt += f"\n\n参考上下文：{context_text}"
            
            # 这里可以调用多模态API生成描述
            # 暂时返回基于类型的描述
            return f"这是一个{content_type}，包含了相关的金融数据信息。"
            
        except Exception as e:
            logger.warning(f"详细描述生成失败: {e}")
            return f"图像类型：{content_type}"
    
    def _extract_key_elements(self, image: Image.Image, extracted_text: str, 
                            content_type: str) -> List[Dict[str, Any]]:
        """提取图像中的关键元素"""
        try:
            key_elements = []
            
            # 从OCR文本中提取数值
            import re
            
            # 提取数字（包括百分比、货币等）
            numbers = re.findall(r'[\d,]+\.?\d*[%￥$]?', extracted_text)
            for num in numbers:
                key_elements.append({
                    "type": "number",
                    "value": num,
                    "context": "extracted_from_ocr"
                })
            
            # 提取日期
            dates = re.findall(r'\d{4}[年\-/]\d{1,2}[月\-/]?\d{0,2}[日]?', extracted_text)
            for date in dates:
                key_elements.append({
                    "type": "date",
                    "value": date,
                    "context": "time_reference"
                })
            
            # 提取金融关键词
            for category, keywords in self.financial_keywords.items():
                for keyword in keywords:
                    if keyword in extracted_text:
                        key_elements.append({
                            "type": "financial_term",
                            "value": keyword,
                            "category": category,
                            "context": "financial_indicator"
                        })
            
            return key_elements
            
        except Exception as e:
            logger.warning(f"关键元素提取失败: {e}")
            return []
    
    def _extract_financial_data(self, extracted_text: str, context_info: Dict[str, str] = None) -> Dict[str, Any]:
        """提取金融数据"""
        try:
            financial_data = {
                "values": [],
                "indicators": [],
                "time_periods": [],
                "currencies": []
            }
            
            import re
            
            # 提取数值和单位
            value_patterns = [
                r'([\d,]+\.?\d*)\s*(万元|亿元|万|亿|元|%)',
                r'([\d,]+\.?\d*)\s*(million|billion|thousand)',
                r'[\$￥]([\d,]+\.?\d*)'
            ]
            
            for pattern in value_patterns:
                matches = re.findall(pattern, extracted_text)
                for match in matches:
                    if isinstance(match, tuple):
                        value, unit = match
                        financial_data["values"].append({
                            "value": value,
                            "unit": unit,
                            "raw_text": f"{value}{unit}"
                        })
            
            # 提取时间周期
            time_patterns = [
                r'(\d{4})[年\-](\d{1,2})[月\-]?(\d{0,2})[日]?',
                r'(Q[1-4])\s*(\d{4})',
                r'(\d{4})\s*(年度|年)',
                r'(第[一二三四]季度|\d+月)'
            ]
            
            for pattern in time_patterns:
                matches = re.findall(pattern, extracted_text)
                financial_data["time_periods"].extend(matches)
            
            return financial_data
            
        except Exception as e:
            logger.warning(f"金融数据提取失败: {e}")
            return {}
    
    def _analyze_chart_content(self, image: Image.Image, extracted_text: str, 
                             content_type: str) -> Dict[str, Any]:
        """分析图表内容"""
        try:
            chart_analysis = {
                "chart_type": content_type,
                "data_series": [],
                "trends": [],
                "key_insights": []
            }
            
            # 基于图表类型的专项分析
            if content_type == "折线图":
                chart_analysis["trends"] = self._analyze_line_chart_trends(extracted_text)
            elif content_type == "柱状图":
                chart_analysis["data_series"] = self._analyze_bar_chart_data(extracted_text)
            elif content_type == "饼图":
                chart_analysis["data_series"] = self._analyze_pie_chart_data(extracted_text)
            
            return chart_analysis
            
        except Exception as e:
            logger.warning(f"图表内容分析失败: {e}")
            return {}
    
    def _analyze_question_relevance(self, user_question: str, analysis_result: Dict[str, Any], 
                                  context_info: Dict[str, str] = None) -> Dict[str, Any]:
        """分析图像内容与用户问题的相关性"""
        try:
            question_lower = user_question.lower()
            relevance = {
                "relevance_score": 0.0,
                "matching_elements": [],
                "suggested_answer_points": []
            }
            
            # 检查问题中的关键词是否在图像内容中出现
            extracted_text = analysis_result.get("extracted_text", "").lower()
            detailed_description = analysis_result.get("detailed_description", "").lower()
            
            # 计算相关性得分
            question_keywords = question_lower.split()
            matching_count = 0
            
            for keyword in question_keywords:
                if len(keyword) > 2:  # 忽略过短的词
                    if keyword in extracted_text or keyword in detailed_description:
                        matching_count += 1
                        relevance["matching_elements"].append(keyword)
            
            if question_keywords:
                relevance["relevance_score"] = matching_count / len(question_keywords)
            
            # 生成建议的回答要点
            if relevance["relevance_score"] > 0.3:
                relevance["suggested_answer_points"] = self._generate_answer_points(
                    user_question, analysis_result
                )
            
            return relevance
            
        except Exception as e:
            logger.warning(f"问题相关性分析失败: {e}")
            return {"relevance_score": 0.0}
    
    def _generate_answer_points(self, user_question: str, analysis_result: Dict[str, Any]) -> List[str]:
        """生成回答要点"""
        try:
            answer_points = []
            
            # 基于图像内容生成回答要点
            content_type = analysis_result.get("content_type", "")
            extracted_text = analysis_result.get("extracted_text", "")
            financial_data = analysis_result.get("financial_data", {})
            
            if "数据" in user_question or "数值" in user_question:
                values = financial_data.get("values", [])
                if values:
                    answer_points.append(f"图像中包含以下数据：{', '.join([v['raw_text'] for v in values[:5]])}")
            
            if "趋势" in user_question or "变化" in user_question:
                if content_type in ["折线图", "柱状图"]:
                    answer_points.append(f"这是一个{content_type}，显示了数据的变化趋势")
            
            if "时间" in user_question or "期间" in user_question:
                time_periods = financial_data.get("time_periods", [])
                if time_periods:
                    answer_points.append(f"时间范围涉及：{', '.join([str(t) for t in time_periods[:3]])}")
            
            return answer_points
            
        except Exception as e:
            logger.warning(f"回答要点生成失败: {e}")
            return []
    
    def _calculate_confidence_score(self, analysis_result: Dict[str, Any]) -> float:
        """计算分析结果的置信度"""
        try:
            score = 0.0
            
            # 基于提取的文本长度
            extracted_text = analysis_result.get("extracted_text", "")
            if len(extracted_text) > 10:
                score += 0.3
            
            # 基于识别的内容类型
            content_type = analysis_result.get("content_type", "")
            if content_type != "未知类型":
                score += 0.2
            
            # 基于关键元素数量
            key_elements = analysis_result.get("key_elements", [])
            if len(key_elements) > 0:
                score += min(0.3, len(key_elements) * 0.1)
            
            # 基于金融数据提取
            financial_data = analysis_result.get("financial_data", {})
            if financial_data.get("values"):
                score += 0.2
            
            return min(1.0, score)
            
        except Exception as e:
            logger.warning(f"置信度计算失败: {e}")
            return 0.0
    
    def _generate_cache_key(self, image_path: str, context_info: Dict[str, str] = None, 
                          user_question: str = None) -> str:
        """生成缓存键"""
        try:
            # 获取图像文件的修改时间和大小
            stat = os.stat(image_path)
            file_info = f"{stat.st_mtime}_{stat.st_size}"
            
            # 包含上下文和问题信息
            context_str = str(sorted(context_info.items())) if context_info else ""
            question_str = user_question or ""
            
            # 生成哈希
            cache_data = f"{image_path}_{file_info}_{context_str}_{question_str}"
            return hashlib.md5(cache_data.encode()).hexdigest()
            
        except Exception as e:
            logger.warning(f"缓存键生成失败: {e}")
            return hashlib.md5(image_path.encode()).hexdigest()
    
    def _cache_analysis_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """缓存分析结果"""
        try:
            if len(self._analysis_cache) >= self._cache_max_size:
                # 删除最旧的缓存项
                oldest_key = next(iter(self._analysis_cache))
                del self._analysis_cache[oldest_key]
            
            self._analysis_cache[cache_key] = result
            
        except Exception as e:
            logger.warning(f"缓存分析结果失败: {e}")
    
    def clear_cache(self) -> None:
        """清理缓存"""
        self._analysis_cache.clear()
        logger.info("图像分析缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self._analysis_cache),
            "cache_max_size": self._cache_max_size
        }
    
    # 辅助方法
    def _analyze_line_chart_trends(self, text: str) -> List[str]:
        """分析折线图趋势"""
        trends = []
        if "上升" in text or "增长" in text or "提高" in text:
            trends.append("上升趋势")
        if "下降" in text or "减少" in text or "降低" in text:
            trends.append("下降趋势")
        if "波动" in text or "变化" in text:
            trends.append("波动趋势")
        return trends
    
    def _analyze_bar_chart_data(self, text: str) -> List[Dict[str, str]]:
        """分析柱状图数据"""
        import re
        data_series = []
        # 简单的数据系列提取
        numbers = re.findall(r'[\d,]+\.?\d*', text)
        for i, num in enumerate(numbers[:5]):  # 限制数量
            data_series.append({"series": f"数据{i+1}", "value": num})
        return data_series
    
    def _analyze_pie_chart_data(self, text: str) -> List[Dict[str, str]]:
        """分析饼图数据"""
        import re
        data_series = []
        # 提取百分比数据
        percentages = re.findall(r'(\d+\.?\d*)%', text)
        for i, pct in enumerate(percentages[:5]):
            data_series.append({"category": f"类别{i+1}", "percentage": f"{pct}%"})
        return data_series
